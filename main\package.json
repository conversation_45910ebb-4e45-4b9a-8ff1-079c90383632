{"name": "indusun", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/pg": "^8.11.11", "bcrypt": "^5.1.1", "framer-motion": "^12.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.485.0", "next": "15.2.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pg": "^8.14.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "recharts": "^2.15.3", "redis": "^4.7.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4", "typescript": "^5"}}