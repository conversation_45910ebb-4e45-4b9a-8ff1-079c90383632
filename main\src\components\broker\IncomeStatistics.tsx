'use client';

import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YA<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface IncomeData {
  name: string;
  value: number;
}

interface IncomeStatisticsProps {
  data: IncomeData[];
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
}

const IncomeStatistics = ({ data, selectedPeriod, onPeriodChange }: IncomeStatisticsProps) => {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium">Income Statistics</h2>
        <select 
          value={selectedPeriod}
          onChange={(e) => onPeriodChange(e.target.value)}
          className="border border-gray-300 rounded-md px-2 py-1 text-sm"
        >
          <option>Monthly</option>
          <option>Weekly</option>
          <option>Daily</option>
        </select>
      </div>
      
      <ResponsiveContainer width="100%" height={250}>
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" axisLine={false} tickLine={false} />
          <YAxis axisLine={false} tickLine={false} />
          <Tooltip />
          <Bar dataKey="value" fill="#8884d8" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default IncomeStatistics;
