# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
*/node_modules
/package-lock.json
*/package-lock.json
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
*/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem
scripts/data/*

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

